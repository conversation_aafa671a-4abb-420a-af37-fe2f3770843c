document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            mainNav.classList.toggle('active');
            const isExpanded = mainNav.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
            if (isExpanded) {
                mobileMenuToggle.innerHTML = '&times;'; // Change to 'X' icon
                mobileMenuToggle.setAttribute('aria-label', 'Close navigation menu');
            } else {
                mobileMenuToggle.innerHTML = '&#9776;'; // Change back to hamburger icon
                mobileMenuToggle.setAttribute('aria-label', 'Open navigation menu');
            }
        });
    }

    // Add smooth scrolling for anchor links if needed
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

});