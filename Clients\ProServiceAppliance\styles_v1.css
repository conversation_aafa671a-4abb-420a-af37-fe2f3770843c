
/* --- Pro Service Appliance Repair & Maintenance --- */
/* --- styles_v1.css --- */

/* --- Core & Brand Styles --- */
:root {
    --service-blue: #005A9C;
    --clean-white: #FFFFFF;
    --action-orange: #F68D2E;
    --light-gray: #F5F5F5;
    --charcoal: #333333;
    --success-green: #28A745;
    --header-font: 'Montserrat', 'Inter', sans-serif;
    --body-font: 'Inter', sans-serif;
    --serif-font: 'Georgia', 'Times New Roman', serif;
}

/* --- Global Resets & Base --- */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--body-font);
    color: var(--charcoal);
    background-color: var(--clean-white);
    line-height: 1.6;
    font-size: 16px;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 0;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--header-font);
    color: var(--service-blue);
    margin-bottom: 1rem;
    line-height: 1.2;
    font-weight: 700;
}

h1 { font-size: 2.8rem; }
h2 { font-size: 2.2rem; }
h3 { font-size: 1.8rem; }

p {
    margin-bottom: 1rem;
}

a {
    color: var(--action-orange);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--service-blue);
}

img {
    max-width: 100%;
    height: auto;
}

/* --- Header & Navigation --- */
.main-header {
    background-color: var(--clean-white);
    padding: 1rem 0;
    border-bottom: 1px solid #ddd;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--service-blue);
}

.logo a {
    color: inherit;
    text-decoration: none;
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 25px;
}

.main-nav a {
    font-weight: 600;
    color: var(--charcoal);
    padding-bottom: 5px;
    border-bottom: 2px solid transparent;
}

.main-nav a:hover, .main-nav a.active {
    color: var(--service-blue);
    border-bottom-color: var(--service-blue);
}

.header-contact {
    text-align: right;
}

.header-contact .phone {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--service-blue);
    display: block;
}

.header-contact .phone:hover {
    color: var(--action-orange);
}

.header-contact .cta-button {
    margin-top: 5px;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    font-size: 2rem;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--service-blue);
}

/* --- Call to Action (CTA) Buttons --- */
.cta-button {
    display: inline-block;
    background-color: var(--action-orange);
    color: var(--clean-white);
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: background-color 0.3s ease, transform 0.2s ease;
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    background-color: #e07b24;
    color: var(--clean-white);
    transform: translateY(-2px);
}

.cta-button-secondary {
    background-color: var(--service-blue);
}
.cta-button-secondary:hover {
    background-color: #004a8a;
}

/* --- Hero Section --- */
.hero {
    background-color: var(--light-gray);
    padding: 60px 0;
    text-align: center;
}

.hero h1 {
    font-size: 3.2rem;
    margin-bottom: 1rem;
}

.hero .tagline {
    font-size: 1.4rem;
    color: var(--charcoal);
    margin-bottom: 2rem;
}

/* --- Service Grid --- */
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 2rem;
}

.service-card {
    background: var(--clean-white);
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 25px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.08);
}

.service-card i {
    font-size: 3rem;
    color: var(--service-blue);
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* --- Why Choose Us Section --- */
.why-choose-us {
    background-color: var(--light-gray);
    padding: 50px 0;
}

.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.feature-item i {
    font-size: 2rem;
    color: var(--action-orange);
    margin-top: 5px;
}

/* --- Footer --- */
.main-footer {
    background-color: var(--charcoal);
    color: var(--clean-white);
    padding: 40px 0 20px;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-col h4 {
    color: var(--clean-white);
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--action-orange);
    padding-bottom: 10px;
    font-size: 1.2rem;
}

.footer-col ul {
    list-style: none;
}

.footer-col li {
    margin-bottom: 10px;
}

.footer-col a {
    color: #ccc;
}

.footer-col a:hover {
    color: var(--action-orange);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #555;
    font-size: 0.9rem;
    color: #ccc;
}

/* --- Forms --- */
.form-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 30px;
    background: var(--light-gray);
    border-radius: 8px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--service-blue);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: var(--body-font);
    font-size: 1rem;
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

/* --- Responsive Design --- */
@media (max-width: 992px) {
    h1 { font-size: 2.5rem; }
    .hero h1 { font-size: 2.8rem; }
}

@media (max-width: 768px) {
    .main-header .container {
        flex-wrap: wrap;
    }
    .main-nav {
        display: none;
        width: 100%;
        flex-direction: column;
        background-color: var(--clean-white);
        margin-top: 1rem;
    }
    .main-nav.active {
        display: flex;
    }
    .main-nav ul {
        flex-direction: column;
        width: 100%;
        gap: 0;
    }
    .main-nav li {
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
    }
    .main-nav a {
        display: block;
        padding: 15px;
        width: 100%;
        border-bottom: none;
    }
    .main-nav a:hover {
        background-color: var(--light-gray);
    }
    .mobile-menu-toggle {
        display: block;
    }
    .header-contact {
        display: none; /* Hide header contact on mobile, CTA in hero is more prominent */
    }
    .footer-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
    .footer-col h4 {
        border-bottom: none;
    }
}

@media (max-width: 576px) {
    h1 { font-size: 2rem; }
    .hero h1 { font-size: 2.2rem; }
    .hero .tagline { font-size: 1.1rem; }
    .container { width: 95%; }
    .cta-button {
        width: 100%;
        padding: 15px;
        font-size: 1.1rem;
    }
}
