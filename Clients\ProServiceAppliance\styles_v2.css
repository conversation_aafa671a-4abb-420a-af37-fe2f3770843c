/* --- General Body & Typography --- */
body {
    font-family: 'Inter', sans-serif;
    color: #333333;
    background-color: #FFFFFF;
    margin: 0;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    color: #005A9C;
}

h1 { font-size: 2.8rem; margin-bottom: 0.5rem; }
h2.section-title { font-size: 2.2rem; text-align: center; margin-bottom: 3rem; }
h3 { font-size: 1.5rem; }

a { color: #005A9C; text-decoration: none; }
a:hover { text-decoration: underline; }

/* --- Header --- */
.main-header {
    background: #FFFFFF;
    border-bottom: 1px solid #F5F5F5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    font-size: 1.5rem;
    font-weight: 800;
    color: #005A9C;
    text-decoration: none;
}

.main-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}

.main-nav li {
    margin-left: 25px;
}

.main-nav a {
    font-weight: 600;
    font-size: 1rem;
    color: #333333;
    padding-bottom: 5px;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s;
}

.main-nav a:hover, .main-nav a.active {
    color: #005A9C;
    border-bottom-color: #F68D2E;
}

.header-contact {
    display: flex;
    align-items: center;
}

.phone-number {
    font-weight: 700;
    font-size: 1.1rem;
    color: #005A9C;
    margin-right: 20px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
}

/* --- Call to Action Buttons --- */
.cta-button {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
    color: #FFFFFF;
    background-color: #F68D2E;
    padding: 16px 32px;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid #F68D2E;
    min-width: 44px;
    min-height: 44px;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.cta-button:hover {
    background-color: #e07b20;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(246, 141, 46, 0.3);
}

.cta-button:focus {
    outline: 3px solid #005A9C;
    outline-offset: 2px;
}

.cta-button:active {
    transform: translateY(0);
}

.cta-button-primary {
    background-color: #F68D2E;
    color: #FFFFFF;
    border-color: #F68D2E;
}

.cta-button-secondary {
    background-color: transparent;
    color: #005A9C;
    border: 2px solid #005A9C;
}

.cta-button-secondary:hover {
    background-color: #005A9C;
    color: #FFFFFF;
    border-color: #005A9C;
}

.cta-button-large {
    font-size: 1.3rem;
    padding: 20px 40px;
}

.cta-button-urgent {
    background: linear-gradient(135deg, #F68D2E 0%, #e07b20 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(246, 141, 46, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(246, 141, 46, 0); }
    100% { box-shadow: 0 0 0 0 rgba(246, 141, 46, 0); }
}

/* --- Hero Section --- */
.hero {
    background: #F5F5F5;
    padding: 80px 0;
    text-align: center;
}

.hero h1 { font-size: 3.5rem; }
.hero .tagline { font-size: 1.2rem; color: #555; max-width: 700px; margin: 1rem auto 2rem; }
.trust-signal-hero { margin-top: 1rem; color: #28A745; font-weight: 600; }

/* --- Services Overview Section --- */
.services-overview { padding: 60px 0; }
.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: #FFFFFF;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.08);
}

.service-card img { margin-bottom: 1rem; }
.service-card h3 { margin-bottom: 0.5rem; }
.service-card .learn-more { font-weight: 700; color: #F68D2E; }

/* --- Why Choose Us CTA Section --- */
.why-choose-us-cta { padding: 60px 0; background: #F5F5F5; }
.why-choose-us-cta .container {
    display: flex;
    align-items: center;
    gap: 40px;
}
.why-choose-us-content { flex: 1; }
.why-choose-us-image { flex: 1; }
.why-choose-us-image img { max-width: 100%; border-radius: 8px; }
.feature-list { list-style: none; padding: 0; margin-top: 1.5rem; }
.feature-list li { margin-bottom: 1rem; font-size: 1.1rem; }

/* --- Testimonials Section --- */
.testimonials { padding: 60px 0; }
.testimonial-card {
    background: #F5F5F5;
    border-left: 5px solid #005A9C;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    border-radius: 0 8px 8px 0;
}
.testimonial-card blockquote { font-size: 1.2rem; margin: 0; font-style: italic; }
.testimonial-card cite { display: block; text-align: right; margin-top: 1rem; font-weight: 600; }
.testimonial-actions { text-align: center; margin-top: 2rem; }

/* --- Final CTA Section --- */
.cta-final {
    background-color: #005A9C;
    color: #FFFFFF;
    padding: 60px 0;
    text-align: center;
}
.cta-final h2 { color: #FFFFFF; font-size: 2.5rem; }
.cta-final p { font-size: 1.2rem; margin-bottom: 2rem; }
.cta-final .cta-button { background-color: #F68D2E; color: #FFFFFF; }

/* --- Footer --- */
.main-footer {
    background: #333333;
    color: #F5F5F5;
    padding: 50px 0 20px;
}
.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}
.footer-col h4 { color: #FFFFFF; }
.footer-col ul { list-style: none; padding: 0; }
.footer-col li { margin-bottom: 10px; }
.footer-col a { color: #F5F5F5; }
.footer-bottom { text-align: center; border-top: 1px solid #555; padding-top: 20px; font-size: 0.9rem; }

/* --- Responsive Design --- */
@media (max-width: 992px) {
    .main-nav, .header-contact .cta-button { display: none; }
    .mobile-menu-toggle { display: block; }
    .main-nav.active { display: flex; flex-direction: column; position: absolute; top: 70px; left: 0; background: #FFF; width: 100%; box-shadow: 0 4px 8px rgba(0,0,0,0.1); padding: 20px; }
    .main-nav.active li { margin: 10px 0; }
    .why-choose-us-cta .container { flex-direction: column; }
}

@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    .hero h1 { font-size: 2.8rem; }
    .header-contact .phone-number { display: none; }
}