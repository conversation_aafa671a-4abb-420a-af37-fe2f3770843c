
/* --- Pro Service Appliance Repair & Maintenance --- */
/* --- scripts_v1.js --- */

document.addEventListener('DOMContentLoaded', function() {

    // --- Mobile Menu Toggle --- //
    const menuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (menuToggle && mainNav) {
        menuToggle.addEventListener('click', () => {
            mainNav.classList.toggle('active');
        });
    }

    // --- Click-to-Call Conversion Tracking (Placeholder) --- //
    // In a real-world scenario, you would integrate with an analytics service.
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    phoneLinks.forEach(link => {
        link.addEventListener('click', () => {
            console.log(`Call initiated to: ${link.href}`);
            // Example: gtag('event', 'conversion', {'send_to': 'AW-CONVERSION_ID/LABEL'});
        });
    });

    // --- Smooth Scrolling for Anchor Links --- //
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            if(targetElement){
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // --- Active Navigation Link Styling --- //
    const currentLocation = window.location.href;
    const navLinks = document.querySelectorAll('.main-nav a');
    navLinks.forEach(link => {
        if (link.href === currentLocation) {
            link.classList.add('active');
        }
    });

});
